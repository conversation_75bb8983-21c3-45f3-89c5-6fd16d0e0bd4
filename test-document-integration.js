const axios = require('axios');

const API_BASE = 'http://localhost:4000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.EeSIj2JtcbUBnYLKEASK5rPBLzIUSj4xWETkBUvSO-Q';

const headers = {
  'Authorization': AUTH_TOKEN,
  'Content-Type': 'application/json'
};

async function testDocumentIntegration() {
  console.log('🧪 Testing Document Integration for Chat Negotiation...\n');

  try {
    // Step 1: Create a mock contract analysis scenario
    console.log('📋 Step 1: Creating negotiation scenario from mock analysis...');
    
    const mockAnalysisId = 'mock-analysis-123';
    
    const scenarioResponse = await axios.post(`${API_BASE}/chat-negotiation/scenarios/from-analysis`, {
      analysisId: mockAnalysisId
    }, { headers });

    console.log('✅ <PERSON><PERSON><PERSON> created successfully:');
    console.log(JSON.stringify(scenarioResponse.data, null, 2));

  } catch (error) {
    if (error.response) {
      console.log('❌ Error creating scenario:');
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  try {
    // Step 2: Create a negotiation session from mock document
    console.log('\n📋 Step 2: Creating negotiation session from mock document...');
    
    const sessionResponse = await axios.post(`${API_BASE}/chat-negotiation/sessions/from-document`, {
      analysisId: 'mock-analysis-123',
      aiPersonality: {
        characterId: 'contract_specialist',
        aggressiveness: 0.6,
        flexibility: 0.4,
        riskTolerance: 0.3,
        communicationStyle: 'ANALYTICAL'
      }
    }, { headers });

    console.log('✅ Session created successfully:');
    console.log(JSON.stringify(sessionResponse.data, null, 2));

    // Step 3: Test getting document context
    console.log('\n📋 Step 3: Getting document context for session...');
    
    const sessionId = sessionResponse.data.id || sessionResponse.data._id;
    const contextResponse = await axios.get(`${API_BASE}/chat-negotiation/sessions/${sessionId}/document-context`, { headers });

    console.log('✅ Document context retrieved:');
    console.log(JSON.stringify(contextResponse.data, null, 2));

  } catch (error) {
    if (error.response) {
      console.log('❌ Error in session/context operations:');
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  try {
    // Step 4: Test regular chat negotiation endpoints
    console.log('\n📋 Step 4: Testing regular chat negotiation endpoints...');
    
    const sessionsResponse = await axios.get(`${API_BASE}/chat-negotiation/sessions`, { headers });
    console.log('✅ Sessions retrieved:');
    console.log(`Found ${sessionsResponse.data.sessions?.length || 0} sessions`);

    // Test data extraction
    const extractResponse = await axios.post(`${API_BASE}/chat-negotiation/extract-data`, {
      message: "I'm thinking we could negotiate the liability cap to $500,000 and include mutual indemnification clauses.",
      context: { contractType: 'SERVICE_AGREEMENT' }
    }, { headers });

    console.log('✅ Data extraction test:');
    console.log(JSON.stringify(extractResponse.data, null, 2));

  } catch (error) {
    if (error.response) {
      console.log('❌ Error in regular endpoints:');
      console.log('Status:', error.response.status);
      console.log('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n🎉 Document Integration Test Complete!');
}

// Run the test
testDocumentIntegration().catch(console.error);
